# Gemini Live Voice Assistant

A terminal-based voice assistant powered by Google's Vertex AI and Gemini Live 2.5 Flash model. This application provides real-time voice interactions with AI through your terminal, supporting both voice input and voice output.

## Features

- 🎤 **Real-time Voice Input**: Continuous audio recording from microphone
- 🔊 **Voice Output**: AI responses played back as audio
- 📝 **Live Transcription**: Real-time transcription of both user input and AI responses
- 🌐 **Vertex AI Integration**: Uses Google Cloud Vertex AI and Gemini Live API
- 🎯 **Voice Activity Detection**: Automatic detection of speech start/end
- 💬 **Conversation History**: Track and display conversation turns
- 🎨 **Rich Terminal UI**: Beautiful terminal interface with real-time updates
- ⚙️ **Configurable**: Extensive configuration options via environment variables

## Prerequisites

- Python 3.8 or higher
- Google Cloud Project with Vertex AI API enabled
- Audio input/output devices (microphone and speakers)
- `uv` package manager (for virtual environment management)

## Installation

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd gemini-terminal-mm-live
   ```

2. **Create and activate virtual environment using uv**:

   ```bash
   uv venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   uv pip install -r requirements.txt
   ```

## Configuration

1. **Copy the example environment file**:

   ```bash
   cp .env.example .env
   ```

2. **Configure your environment variables** in `.env`:

   ### Google Cloud Configuration

   ```bash
   # Option 1: Use Vertex AI (recommended)
   GOOGLE_CLOUD_PROJECT=your-project-id
   GOOGLE_CLOUD_LOCATION=us-central1
   GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

   # Option 2: Use Gemini API directly
   GEMINI_API_KEY=your-gemini-api-key
   ```

   ### Audio Configuration

   ```bash
   AUDIO_SAMPLE_RATE=16000
   AUDIO_CHANNELS=1
   AUDIO_CHUNK_SIZE=1024
   AUDIO_FORMAT=int16
   ```

   ### Voice Assistant Configuration

   ```bash
   GEMINI_MODEL=gemini-live-2.5-flash
   VOICE_NAME=Aoede
   LANGUAGE_CODE=en-US
   RESPONSE_MODALITY=AUDIO
   ENABLE_TRANSCRIPTION=true
   ```

## Authentication Setup

### Option 1: Vertex AI (Recommended)

1. **Install Google Cloud CLI**:

   ```bash
   # macOS
   brew install google-cloud-sdk

   # Ubuntu/Debian
   sudo apt-get install google-cloud-cli
   ```

2. **Authenticate with Google Cloud**:

   ```bash
   gcloud auth login
   gcloud auth application-default login
   gcloud config set project YOUR_PROJECT_ID
   ```

3. **Enable required APIs**:
   ```bash
   gcloud services enable aiplatform.googleapis.com
   ```

### Option 2: Service Account Key

1. **Create a service account** in Google Cloud Console
2. **Download the JSON key file**
3. **Set the environment variable**:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
   ```

### Option 3: Gemini API Key

1. **Get an API key** from [Google AI Studio](https://aistudio.google.com/)
2. **Set the environment variable**:
   ```bash
   export GEMINI_API_KEY="your-api-key"
   ```

## Usage

### Basic Usage

1. **Activate the virtual environment**:

   ```bash
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Run the voice assistant**:

   ```bash
   python main.py
   ```

3. **Start talking!** The assistant will:
   - Listen for your voice input
   - Process your speech with Gemini Live
   - Respond with voice output
   - Display transcriptions in the terminal

### Command Line Options

```bash
python main.py --help
```

Available options:

- `--config, -c`: Path to configuration file (.env)
- `--log-level, -l`: Set logging level (DEBUG, INFO, WARNING, ERROR)
- `--model, -m`: Gemini model to use (default: gemini-live-2.5-flash)
- `--voice, -v`: Voice to use for responses (default: Aoede)

### Example Commands

```bash
# Use debug logging
python main.py --log-level DEBUG

# Use a different voice
python main.py --voice Charon

# Use a custom config file
python main.py --config my-config.env
```

## Terminal Interface

The terminal interface provides:

- **Header**: Application title and basic instructions
- **Conversation Panel**: Real-time conversation history with transcriptions
- **Status Panel**: Current state, connection status, and conversation statistics
- **Footer**: Available keyboard commands

### Keyboard Commands

- `Ctrl+C`: Quit the application
- `q`: Quit (when implemented)
- `t`: Send text message (when implemented)
- `c`: Clear conversation history (when implemented)
- `h`: Show help (when implemented)

## Project Structure

```
gemini-terminal-mm-live/
├── src/voice_assistant/
│   ├── __init__.py          # Package initialization
│   ├── config.py            # Configuration management
│   ├── audio_handler.py     # Audio input/output handling
│   ├── gemini_client.py     # Gemini Live API client
│   ├── voice_assistant.py   # Main voice assistant logic
│   └── utils.py             # Utility functions
├── tests/                   # Test files
├── config/                  # Configuration files
├── logs/                    # Log files
├── main.py                  # Main application entry point
├── requirements.txt         # Python dependencies
├── .env.example            # Example environment configuration
└── README.md               # This file
```

## Troubleshooting

### Common Issues

#### Audio Issues

**Problem**: No audio input/output detected

```bash
# List available audio devices
python -c "import pyaudio; p = pyaudio.PyAudio(); [print(f'{i}: {p.get_device_info_by_index(i)[\"name\"]}') for i in range(p.get_device_count())]"
```

**Solution**: Set specific audio devices in `.env`:

```bash
AUDIO_INPUT_DEVICE=1   # Use device index from list above
AUDIO_OUTPUT_DEVICE=2  # Use device index from list above
```

#### Authentication Issues

**Problem**: `DefaultCredentialsError`

```
google.auth.exceptions.DefaultCredentialsError: Could not automatically determine credentials
```

**Solution**: Set up authentication properly:

```bash
# Option 1: Use gcloud CLI
gcloud auth application-default login

# Option 2: Set service account key
export GOOGLE_APPLICATION_CREDENTIALS="path/to/key.json"

# Option 3: Use API key
export GEMINI_API_KEY="your-api-key"
```

#### Connection Issues

**Problem**: Failed to connect to Gemini Live API

**Solutions**:

1. Check your internet connection
2. Verify your Google Cloud project has Vertex AI API enabled
3. Ensure your credentials have proper permissions
4. Try using a different model or region

#### Dependencies Issues

**Problem**: Package installation failures

**Solutions**:

```bash
# Update uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# Clear cache and reinstall
uv cache clean
uv pip install -r requirements.txt --force-reinstall

# Install system dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install portaudio19-dev python3-dev

# Install system dependencies (macOS)
brew install portaudio
```

### Debug Mode

Run with debug logging to get detailed information:

```bash
python main.py --log-level DEBUG
```

### Log Files

Check log files for detailed error information:

```bash
tail -f logs/voice_assistant.log
```

## Development

### Setting up Development Environment

1. **Clone and setup**:

   ```bash
   git clone <repository-url>
   cd gemini-terminal-mm-live
   uv venv venv
   source venv/bin/activate
   uv pip install -r requirements.txt
   ```

2. **Install development dependencies**:

   ```bash
   uv pip install pytest pytest-asyncio black flake8
   ```

3. **Run tests**:

   ```bash
   pytest tests/
   ```

4. **Format code**:
   ```bash
   black src/ main.py
   flake8 src/ main.py
   ```

### Architecture

The application follows a modular architecture:

- **Config Module**: Handles configuration loading and validation
- **Audio Handler**: Manages audio input/output operations
- **Gemini Client**: Handles communication with Gemini Live API
- **Voice Assistant**: Orchestrates the conversation flow
- **Terminal Interface**: Provides the user interface

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Google Cloud Vertex AI team for the Gemini Live API
- Rich library for beautiful terminal interfaces
- PyAudio for audio processing capabilities

## Support

For issues and questions:

1. Check the troubleshooting section above
2. Search existing GitHub issues
3. Create a new issue with detailed information

---

**Note**: This is a demonstration project. For production use, consider additional security measures, error handling, and performance optimizations.
