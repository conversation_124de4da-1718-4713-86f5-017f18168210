#!/usr/bin/env python3
"""
Terminal-based Voice Assistant using Vertex AI and Gemini Live API.

This is the main entry point for the voice assistant application.
It provides a terminal interface for interacting with the voice assistant.
"""

from voice_assistant import Config, VoiceAssistant, ConversationState
import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.live import Live
from rich.layout import Layout
from rich.table import Table
from rich import box

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))


console = Console()
logger = logging.getLogger(__name__)


class TerminalInterface:
    """Terminal interface for the voice assistant."""

    def __init__(self, assistant: VoiceAssistant):
        """Initialize the terminal interface."""
        self.assistant = assistant
        self.layout = Layout()
        self.conversation_text = Text()
        self.status_text = Text()
        self.is_running = False

        # Set up layout
        self._setup_layout()

        # Set up assistant callbacks
        self.assistant.set_callbacks(
            on_state_change=self._on_state_change,
            on_transcription=self._on_transcription,
            on_error=self._on_error,
        )

    def _setup_layout(self) -> None:
        """Set up the terminal layout."""
        self.layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=5),
        )

        self.layout["main"].split_row(
            Layout(name="conversation", ratio=3),
            Layout(name="status", ratio=1),
        )

    def _create_header(self) -> Panel:
        """Create the header panel."""
        title = Text("🎤 Gemini Live Voice Assistant", style="bold blue")
        subtitle = Text(
            "Press 'q' to quit, 't' to send text message", style="dim")

        header_text = Text()
        header_text.append(title)
        header_text.append("\n")
        header_text.append(subtitle)

        return Panel(
            header_text,
            box=box.ROUNDED,
            style="blue",
        )

    def _create_conversation_panel(self) -> Panel:
        """Create the conversation panel."""
        return Panel(
            self.conversation_text,
            title="Conversation",
            box=box.ROUNDED,
            style="green",
        )

    def _create_status_panel(self) -> Panel:
        """Create the status panel."""
        status_table = Table(show_header=False, box=None)
        status_table.add_column("Label", style="bold")
        status_table.add_column("Value")

        # Assistant state
        state_color = self._get_state_color(self.assistant.current_state)
        status_table.add_row(
            "State:",
            Text(self.assistant.current_state.value.title(), style=state_color)
        )

        # Connection status
        connection_status = "Connected" if self.assistant.connected else "Disconnected"
        connection_color = "green" if self.assistant.connected else "red"
        status_table.add_row(
            "Connection:",
            Text(connection_status, style=connection_color)
        )

        # Conversation turns
        status_table.add_row(
            "Turns:",
            str(len(self.assistant.get_conversation_history()))
        )

        return Panel(
            status_table,
            title="Status",
            box=box.ROUNDED,
            style="yellow",
        )

    def _create_footer(self) -> Panel:
        """Create the footer panel."""
        commands = [
            ("q", "Quit"),
            ("t", "Send text message"),
            ("c", "Clear conversation"),
            ("h", "Show help"),
        ]

        footer_text = Text()
        for i, (key, desc) in enumerate(commands):
            if i > 0:
                footer_text.append(" | ")
            footer_text.append(f"[{key}]", style="bold cyan")
            footer_text.append(f" {desc}")

        return Panel(
            footer_text,
            title="Commands",
            box=box.ROUNDED,
            style="cyan",
        )

    def _get_state_color(self, state: ConversationState) -> str:
        """Get color for conversation state."""
        color_map = {
            ConversationState.IDLE: "white",
            ConversationState.LISTENING: "green",
            ConversationState.PROCESSING: "yellow",
            ConversationState.SPEAKING: "blue",
            ConversationState.ERROR: "red",
        }
        return color_map.get(state, "white")

    def _on_state_change(self, old_state: ConversationState, new_state: ConversationState) -> None:
        """Handle state change from assistant."""
        logger.debug(f"State changed: {old_state.value} -> {new_state.value}")

    def _on_transcription(self, transcription: str) -> None:
        """Handle transcription from assistant."""
        self.conversation_text.append(f"\n{transcription}")

        # Keep only last 50 lines
        lines = str(self.conversation_text).split('\n')
        if len(lines) > 50:
            lines = lines[-50:]
            self.conversation_text = Text('\n'.join(lines))

    def _on_error(self, error: Exception) -> None:
        """Handle error from assistant."""
        error_text = f"\n❌ Error: {str(error)}"
        self.conversation_text.append(error_text, style="red")

    def update_display(self) -> Layout:
        """Update the display layout."""
        self.layout["header"].update(self._create_header())
        self.layout["conversation"].update(self._create_conversation_panel())
        self.layout["status"].update(self._create_status_panel())
        self.layout["footer"].update(self._create_footer())

        return self.layout


async def run_assistant(config_file: Optional[str] = None) -> None:
    """Run the voice assistant."""
    try:
        # Load configuration
        config = Config.from_env(config_file)
        config.setup_logging()
        config.validate()

        logger.info("Starting Gemini Live Voice Assistant")

        # Create voice assistant
        assistant = VoiceAssistant(config)

        # Create terminal interface
        interface = TerminalInterface(assistant)

        # Set up signal handlers
        def signal_handler(signum, frame):
            logger.info("Received interrupt signal, shutting down...")
            interface.is_running = False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Start the assistant
        async with assistant:
            interface.is_running = True

            # Create live display
            with Live(
                interface.update_display(),
                refresh_per_second=4
            ) as live:
                console.print(
                    "🎤 Voice Assistant started! Speak to interact...")
                console.print("Press Ctrl+C to quit")

                # Main loop
                while interface.is_running and assistant.running:
                    try:
                        # Update display
                        live.update(interface.update_display())

                        # Handle keyboard input (non-blocking)
                        await asyncio.sleep(0.1)

                    except KeyboardInterrupt:
                        logger.info("Keyboard interrupt received")
                        break
                    except Exception as e:
                        logger.error(f"Error in main loop: {e}")
                        break

        console.print("👋 Voice Assistant stopped. Goodbye!")

    except Exception as e:
        console.print(f"❌ Failed to start voice assistant: {e}", style="red")
        logger.error(f"Failed to start voice assistant: {e}")
        sys.exit(1)


@click.command()
@click.option(
    "--config",
    "-c",
    type=click.Path(exists=True),
    help="Path to configuration file (.env)",
)
@click.option(
    "--log-level",
    "-l",
    type=click.Choice(["DEBUG", "INFO", "WARNING", "ERROR"]),
    default="INFO",
    help="Set logging level",
)
@click.option(
    "--model",
    "-m",
    default="gemini-live-2.5-flash",
    help="Gemini model to use",
)
@click.option(
    "--voice",
    "-v",
    default="Aoede",
    help="Voice to use for responses",
)
def main(
    config: Optional[str],
    log_level: str,
    model: str,
    voice: str
) -> None:
    """Terminal-based Voice Assistant using Vertex AI and Gemini Live API."""

    # Set environment variables from CLI options
    import os
    if log_level:
        os.environ["LOG_LEVEL"] = log_level
    if model:
        os.environ["GEMINI_MODEL"] = model
    if voice:
        os.environ["VOICE_NAME"] = voice

    # Run the assistant
    try:
        asyncio.run(run_assistant(config))
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!")
    except Exception as e:
        console.print(f"❌ Error: {e}", style="red")
        sys.exit(1)


if __name__ == "__main__":
    main()
