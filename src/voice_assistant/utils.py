"""
Utility functions for the Voice Assistant.

This module provides utility functions for error handling,
logging, and other common operations.
"""

import logging
import functools
import traceback
from typing import Callable, Any, Optional
from pathlib import Path


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    format_string: Optional[str] = None
) -> None:
    """Set up logging configuration."""
    if format_string is None:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Convert string level to logging level
    numeric_level = getattr(logging, level.upper(), logging.INFO)

    # Create handlers
    handlers = []

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(format_string))
    handlers.append(console_handler)

    # File handler if specified
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(format_string))
        handlers.append(file_handler)

    # Configure root logger
    logging.basicConfig(
        level=numeric_level,
        format=format_string,
        handlers=handlers,
        force=True
    )


def handle_exceptions(
    logger: Optional[logging.Logger] = None,
    reraise: bool = True,
    default_return: Any = None
) -> Callable:
    """Decorator to handle exceptions in functions."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if logger:
                    logger.error(
                        f"Exception in {func.__name__}: {e}\n"
                        f"Traceback: {traceback.format_exc()}"
                    )

                if reraise:
                    raise
                else:
                    return default_return

        return wrapper
    return decorator


def handle_async_exceptions(
    logger: Optional[logging.Logger] = None,
    reraise: bool = True,
    default_return: Any = None
) -> Callable:
    """Decorator to handle exceptions in async functions."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if logger:
                    logger.error(
                        f"Exception in {func.__name__}: {e}\n"
                        f"Traceback: {traceback.format_exc()}"
                    )

                if reraise:
                    raise
                else:
                    return default_return

        return wrapper
    return decorator


def safe_call(func: Callable, *args, **kwargs) -> tuple[bool, Any]:
    """Safely call a function and return success status and result."""
    try:
        result = func(*args, **kwargs)
        return True, result
    except Exception as e:
        return False, e


async def safe_async_call(func: Callable, *args, **kwargs) -> tuple[bool, Any]:
    """Safely call an async function and return success status and result."""
    try:
        result = await func(*args, **kwargs)
        return True, result
    except Exception as e:
        return False, e


def validate_audio_format(
    sample_rate: int,
    channels: int,
    format_type: str
) -> bool:
    """Validate audio format parameters."""
    valid_sample_rates = [8000, 16000, 22050, 44100, 48000]
    valid_channels = [1, 2]
    valid_formats = ["int16", "int32", "float32"]

    return (
        sample_rate in valid_sample_rates and
        channels in valid_channels and
        format_type in valid_formats
    )


def format_duration(seconds: float) -> str:
    """Format duration in seconds to human-readable string."""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"


def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text to maximum length with ellipsis."""
    if len(text) <= max_length:
        return text
    return text[:max_length - 3] + "..."


class ErrorHandler:
    """Centralized error handler for the voice assistant."""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize the error handler."""
        self.logger = logger or logging.getLogger(__name__)
        self.error_count = 0
        self.last_error = None

    def handle_error(
        self,
        error: Exception,
        context: str = "",
        critical: bool = False
    ) -> None:
        """Handle an error with logging and tracking."""
        self.error_count += 1
        self.last_error = error

        error_msg = (
            f"Error in {context}: {error}" if context
            else f"Error: {error}"
        )

        if critical:
            self.logger.critical(f"CRITICAL - {error_msg}")
            self.logger.critical(f"Traceback: {traceback.format_exc()}")
        else:
            self.logger.error(error_msg)
            self.logger.debug(f"Traceback: {traceback.format_exc()}")

    def reset_error_count(self) -> None:
        """Reset the error count."""
        self.error_count = 0
        self.last_error = None

    @property
    def has_errors(self) -> bool:
        """Check if there have been any errors."""
        return self.error_count > 0
