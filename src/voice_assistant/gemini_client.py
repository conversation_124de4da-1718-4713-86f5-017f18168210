"""
Gemini Live API client for real-time voice interactions.

This module handles WebSocket connections to the Gemini Live API,
manages real-time audio streaming, and handles session lifecycle.
"""

import logging
from typing import Optional, Callable, Dict, Any
from google import genai
from google.genai import types

from .config import GeminiConfig


logger = logging.getLogger(__name__)


class GeminiLiveClient:
    """Client for interacting with Gemini Live API."""

    def __init__(self, config: GeminiConfig):
        """Initialize the Gemini Live client."""
        self.config = config
        self.client = None
        self.session = None
        self.is_connected = False
        self.is_streaming = False

        # Callbacks
        self.on_audio_response: Optional[Callable[[bytes], None]] = None
        self.on_text_response: Optional[Callable[[str], None]] = None
        self.on_transcription: Optional[Callable[[str], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
        self.on_session_end: Optional[Callable[[], None]] = None

        logger.info(f"GeminiLiveClient initialized with model: {config.model}")

    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()

    async def connect(self) -> None:
        """Connect to Gemini Live API."""
        try:
            # Initialize the client
            if self.config.api_key:
                # Use API key authentication
                self.client = genai.Client(api_key=self.config.api_key)
            else:
                # Use Vertex AI authentication
                self.client = genai.Client(
                    vertexai=True,
                    project=self.config.project_id,
                    location=self.config.location,
                )

            # Configure session
            session_config = self._build_session_config()

            # Connect to Live API
            self.session = await self.client.aio.live.connect(
                model=self.config.model,
                config=session_config
            )

            self.is_connected = True
            logger.info("Connected to Gemini Live API")

        except Exception as e:
            logger.error(f"Failed to connect to Gemini Live API: {e}")
            raise RuntimeError(f"Connection failed: {e}") from e

    async def disconnect(self) -> None:
        """Disconnect from Gemini Live API."""
        if not self.is_connected:
            return

        try:
            self.is_streaming = False

            if self.session:
                await self.session.close()
                self.session = None

            self.is_connected = False
            logger.info("Disconnected from Gemini Live API")

        except Exception as e:
            logger.error(f"Error during disconnect: {e}")

    def _build_session_config(self) -> Dict[str, Any]:
        """Build session configuration for Gemini Live API."""
        config = {
            "response_modalities": [self.config.response_modality],
            "system_instruction": self.config.system_instruction,
        }

        # Add speech configuration for audio responses
        if self.config.response_modality == "AUDIO":
            config["speech_config"] = {
                "voice_config": {
                    "prebuilt_voice_config": {
                        "voice_name": self.config.voice_name
                    }
                }
            }

            # Add language code if not using native audio
            if "native-audio" not in self.config.model:
                config["speech_config"]["language_code"] = (
                    self.config.language_code
                )

        # Add transcription configuration
        if self.config.enable_transcription:
            config["input_audio_transcription"] = {}
            config["output_audio_transcription"] = {}

        # Add voice activity detection configuration
        if self.config.enable_vad:
            config["realtime_input_config"] = {
                "automatic_activity_detection": {
                    "disabled": False,
                    "start_of_speech_sensitivity": "START_SENSITIVITY_MEDIUM",
                    "end_of_speech_sensitivity": "END_SENSITIVITY_MEDIUM",
                    "prefix_padding_ms": 100,
                    "silence_duration_ms": 500,
                }
            }

        return config

    async def send_audio(self, audio_data: bytes) -> None:
        """Send audio data to Gemini Live API."""
        if not self.is_connected or not self.session:
            raise RuntimeError("Not connected to Gemini Live API")

        try:
            # Create audio blob
            audio_blob = types.Blob(
                data=audio_data,
                mime_type="audio/pcm;rate=16000"
            )

            # Send realtime input
            await self.session.send_realtime_input(audio=audio_blob)

        except Exception as e:
            logger.error(f"Failed to send audio: {e}")
            if self.on_error:
                self.on_error(e)

    async def send_text(self, text: str) -> None:
        """Send text message to Gemini Live API."""
        if not self.is_connected or not self.session:
            raise RuntimeError("Not connected to Gemini Live API")

        try:
            # Send client content
            await self.session.send_client_content(
                turns={"role": "user", "parts": [{"text": text}]},
                turn_complete=True
            )

        except Exception as e:
            logger.error(f"Failed to send text: {e}")
            if self.on_error:
                self.on_error(e)

    async def start_listening(self) -> None:
        """Start listening for responses from Gemini Live API."""
        if not self.is_connected or not self.session:
            raise RuntimeError("Not connected to Gemini Live API")

        self.is_streaming = True
        logger.info("Started listening for Gemini responses")

        try:
            async for response in self.session.receive():
                if not self.is_streaming:
                    break

                await self._handle_response(response)

        except Exception as e:
            logger.error(f"Error while listening: {e}")
            if self.on_error:
                self.on_error(e)
        finally:
            self.is_streaming = False
            logger.info("Stopped listening for Gemini responses")

    async def stop_listening(self) -> None:
        """Stop listening for responses."""
        self.is_streaming = False

    async def _handle_response(self, response) -> None:
        """Handle response from Gemini Live API."""
        try:
            # Handle audio data
            if hasattr(response, 'data') and response.data is not None:
                if self.on_audio_response:
                    self.on_audio_response(response.data)

            # Handle text response
            if hasattr(response, 'text') and response.text is not None:
                if self.on_text_response:
                    self.on_text_response(response.text)

            # Handle server content
            if hasattr(response, 'server_content') and response.server_content:
                server_content = response.server_content

                # Handle input transcription
                if (hasattr(server_content, 'input_transcription') and
                    server_content.input_transcription and
                        hasattr(server_content.input_transcription, 'text')):
                    transcription = server_content.input_transcription.text
                    logger.info(f"Input transcription: {transcription}")
                    if self.on_transcription:
                        self.on_transcription(f"User: {transcription}")

                # Handle output transcription
                if (hasattr(server_content, 'output_transcription') and
                    server_content.output_transcription and
                        hasattr(server_content.output_transcription, 'text')):
                    transcription = server_content.output_transcription.text
                    logger.info(f"Output transcription: {transcription}")
                    if self.on_transcription:
                        self.on_transcription(f"Assistant: {transcription}")

                # Handle interruption
                if (hasattr(server_content, 'interrupted') and
                        server_content.interrupted):
                    logger.info("Generation was interrupted")

                # Handle turn complete
                if (hasattr(server_content, 'turn_complete') and
                        server_content.turn_complete):
                    logger.debug("Turn completed")

            # Handle usage metadata
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                usage = response.usage_metadata
                logger.debug(
                    f"Token usage: {usage.total_token_count} total tokens")

        except Exception as e:
            logger.error(f"Error handling response: {e}")
            if self.on_error:
                self.on_error(e)

    async def send_audio_stream_end(self) -> None:
        """Send audio stream end signal."""
        if not self.is_connected or not self.session:
            raise RuntimeError("Not connected to Gemini Live API")

        try:
            await self.session.send_realtime_input(audio_stream_end=True)
            logger.debug("Sent audio stream end signal")

        except Exception as e:
            logger.error(f"Failed to send audio stream end: {e}")
            if self.on_error:
                self.on_error(e)

    def set_callbacks(
        self,
        on_audio_response: Optional[Callable[[bytes], None]] = None,
        on_text_response: Optional[Callable[[str], None]] = None,
        on_transcription: Optional[Callable[[str], None]] = None,
        on_error: Optional[Callable[[Exception], None]] = None,
        on_session_end: Optional[Callable[[], None]] = None,
    ) -> None:
        """Set callback functions for handling responses."""
        if on_audio_response:
            self.on_audio_response = on_audio_response
        if on_text_response:
            self.on_text_response = on_text_response
        if on_transcription:
            self.on_transcription = on_transcription
        if on_error:
            self.on_error = on_error
        if on_session_end:
            self.on_session_end = on_session_end

    @property
    def connected(self) -> bool:
        """Check if client is connected."""
        return self.is_connected

    @property
    def streaming(self) -> bool:
        """Check if client is streaming."""
        return self.is_streaming
