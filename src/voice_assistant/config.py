"""
Configuration management for the Voice Assistant.

This module handles loading configuration from environment variables,
setting up Google Cloud authentication, and providing configuration
objects for the voice assistant components.
"""

import os
import logging
from pathlib import Path
from typing import Optional, Any
from dataclasses import dataclass, field
from dotenv import load_dotenv
from google.auth import default
from google.auth.exceptions import DefaultCredentialsError


@dataclass
class AudioConfig:
    """Audio configuration settings."""
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    format: str = "int16"
    input_device: Optional[int] = None
    output_device: Optional[int] = None


@dataclass
class GeminiConfig:
    """Gemini API configuration settings."""
    api_key: Optional[str] = None
    model: str = "gemini-live-2.5-flash"
    project_id: Optional[str] = None
    location: str = "us-central1"
    voice_name: str = "Aoede"
    language_code: str = "en-US"
    response_modality: str = "AUDIO"
    enable_transcription: bool = True
    enable_vad: bool = True
    system_instruction: str = (
        "You are a helpful voice assistant. "
        "Respond naturally and conversationally."
    )


@dataclass
class LoggingConfig:
    """Logging configuration settings."""
    level: str = "INFO"
    file: Optional[str] = None
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    console_output: bool = True


@dataclass
class Config:
    """Main configuration class for the Voice Assistant."""
    audio: AudioConfig = field(default_factory=AudioConfig)
    gemini: GeminiConfig = field(default_factory=GeminiConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)

    @classmethod
    def from_env(cls, env_file: Optional[str] = None) -> "Config":
        """Load configuration from environment variables."""
        if env_file:
            load_dotenv(env_file)
        else:
            # Try to load from .env file in current directory
            env_path = Path(".env")
            if env_path.exists():
                load_dotenv(env_path)

        # Audio configuration
        audio_config = AudioConfig(
            sample_rate=int(os.getenv("AUDIO_SAMPLE_RATE", "16000")),
            channels=int(os.getenv("AUDIO_CHANNELS", "1")),
            chunk_size=int(os.getenv("AUDIO_CHUNK_SIZE", "1024")),
            format=os.getenv("AUDIO_FORMAT", "int16"),
            input_device=_get_optional_int("AUDIO_INPUT_DEVICE"),
            output_device=_get_optional_int("AUDIO_OUTPUT_DEVICE"),
        )

        # Gemini configuration
        gemini_config = GeminiConfig(
            api_key=os.getenv("GEMINI_API_KEY"),
            model=os.getenv("GEMINI_MODEL", "gemini-live-2.5-flash"),
            project_id=os.getenv("GOOGLE_CLOUD_PROJECT"),
            location=os.getenv("GOOGLE_CLOUD_LOCATION", "us-central1"),
            voice_name=os.getenv("VOICE_NAME", "Aoede"),
            language_code=os.getenv("LANGUAGE_CODE", "en-US"),
            response_modality=os.getenv("RESPONSE_MODALITY", "AUDIO"),
            enable_transcription=_get_bool("ENABLE_TRANSCRIPTION", True),
            enable_vad=_get_bool("ENABLE_VAD", True),
            system_instruction=os.getenv(
                "SYSTEM_INSTRUCTION",
                "You are a helpful voice assistant. "
                "Respond naturally and conversationally."
            ),
        )

        # Logging configuration
        logging_config = LoggingConfig(
            level=os.getenv("LOG_LEVEL", "INFO"),
            file=os.getenv("LOG_FILE"),
            format=os.getenv(
                "LOG_FORMAT",
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            ),
            console_output=_get_bool("LOG_CONSOLE", True),
        )

        return cls(
            audio=audio_config,
            gemini=gemini_config,
            logging=logging_config,
        )

    def setup_logging(self) -> None:
        """Set up logging based on configuration."""
        level = getattr(logging, self.logging.level.upper(), logging.INFO)

        # Create logs directory if needed
        if self.logging.file:
            log_path = Path(self.logging.file)
            log_path.parent.mkdir(parents=True, exist_ok=True)

        # Configure logging
        handlers = []

        if self.logging.console_output:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(
                logging.Formatter(self.logging.format))
            handlers.append(console_handler)

        if self.logging.file:
            file_handler = logging.FileHandler(self.logging.file)
            file_handler.setFormatter(logging.Formatter(self.logging.format))
            handlers.append(file_handler)

        logging.basicConfig(
            level=level,
            format=self.logging.format,
            handlers=handlers,
            force=True,
        )

    def setup_google_auth(self) -> tuple[Any, str]:
        """Set up Google Cloud authentication."""
        try:
            # Try to get default credentials
            credentials, project = default()

            # Use project from config if available,
            # otherwise use detected project
            if self.gemini.project_id:
                project = self.gemini.project_id

            if not project:
                raise ValueError(
                    "No Google Cloud project found. Please set "
                    "GOOGLE_CLOUD_PROJECT environment variable or "
                    "configure default credentials."
                )

            return credentials, project

        except DefaultCredentialsError as e:
            raise ValueError(
                f"Google Cloud credentials not found: {e}\n"
                "Please set up authentication by:\n"
                "1. Setting GOOGLE_APPLICATION_CREDENTIALS environment "
                "variable, or\n"
                "2. Running 'gcloud auth application-default login', or\n"
                "3. Setting up a service account key"
            ) from e

    def validate(self) -> None:
        """Validate the configuration."""
        errors = []

        # Validate Gemini configuration
        if not self.gemini.api_key and not self.gemini.project_id:
            errors.append(
                "Either GEMINI_API_KEY or GOOGLE_CLOUD_PROJECT must be set"
            )

        if self.gemini.response_modality not in ["AUDIO", "TEXT"]:
            errors.append(
                f"Invalid response modality: {self.gemini.response_modality}. "
                "Must be 'AUDIO' or 'TEXT'"
            )

        # Validate audio configuration
        if self.audio.sample_rate not in [8000, 16000, 22050, 44100, 48000]:
            errors.append(
                f"Invalid sample rate: {self.audio.sample_rate}. "
                "Recommended: 16000 for Gemini Live API"
            )

        if self.audio.channels not in [1, 2]:
            errors.append(
                f"Invalid number of channels: {self.audio.channels}. "
                "Must be 1 (mono) or 2 (stereo)"
            )

        if errors:
            raise ValueError(
                "Configuration validation failed:\n" + "\n".join(errors))


def _get_optional_int(env_var: str) -> Optional[int]:
    """Get an optional integer from environment variable."""
    value = os.getenv(env_var)
    return int(value) if value is not None else None


def _get_bool(env_var: str, default: bool = False) -> bool:
    """Get a boolean from environment variable."""
    value = os.getenv(env_var, "").lower()
    if value in ("true", "1", "yes", "on"):
        return True
    elif value in ("false", "0", "no", "off"):
        return False
    else:
        return default
