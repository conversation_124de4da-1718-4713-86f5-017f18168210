"""
Main Voice Assistant implementation.

This module implements the core voice assistant functionality,
managing conversation flow, voice activity detection, and
coordinating between audio handling and Gemini Live API.
"""

import asyncio
import logging
import time
from typing import Optional, List
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from .config import Config
from .audio_handler import <PERSON>Handler
from .gemini_client import GeminiLiveClient


logger = logging.getLogger(__name__)


class ConversationState(Enum):
    """States of the conversation."""
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    SPEAKING = "speaking"
    ERROR = "error"


@dataclass
class ConversationTurn:
    """Represents a single turn in the conversation."""
    timestamp: datetime
    speaker: str  # "user" or "assistant"
    content: str
    content_type: str  # "text" or "audio"
    transcription: Optional[str] = None


class VoiceAssistant:
    """Main voice assistant class."""

    def __init__(self, config: Config):
        """Initialize the voice assistant."""
        self.config = config
        self.audio_handler = AudioHandler(config.audio)
        self.gemini_client = GeminiLiveClient(config.gemini)

        # State management
        self.state = ConversationState.IDLE
        self.is_running = False
        self.conversation_history: List[ConversationTurn] = []

        # Audio processing
        self.audio_buffer = bytearray()
        self.is_user_speaking = False
        self.last_audio_time = time.time()
        self.silence_threshold = 1.0  # seconds of silence before processing

        # Callbacks for UI updates
        self.on_state_change: Optional[callable] = None
        self.on_transcription: Optional[callable] = None
        self.on_error: Optional[callable] = None

        # Threading
        self._audio_processing_task = None
        self._conversation_task = None

        logger.info("VoiceAssistant initialized")

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()

    async def start(self) -> None:
        """Start the voice assistant."""
        if self.is_running:
            logger.warning("Voice assistant is already running")
            return

        try:
            logger.info("Starting voice assistant...")

            # Initialize audio handler
            self.audio_handler.initialize()

            # Connect to Gemini Live API
            await self.gemini_client.connect()

            # Set up callbacks
            self._setup_callbacks()

            # Start audio processing
            self.audio_handler.start_recording(callback=self._on_audio_data)
            self.audio_handler.start_playback()

            # Start Gemini listening
            self._conversation_task = asyncio.create_task(
                self.gemini_client.start_listening()
            )

            self.is_running = True
            self._set_state(ConversationState.LISTENING)

            logger.info("Voice assistant started successfully")

        except Exception as e:
            logger.error(f"Failed to start voice assistant: {e}")
            await self.stop()
            raise RuntimeError(f"Voice assistant start failed: {e}") from e

    async def stop(self) -> None:
        """Stop the voice assistant."""
        if not self.is_running:
            return

        logger.info("Stopping voice assistant...")

        self.is_running = False
        self._set_state(ConversationState.IDLE)

        # Stop audio processing
        self.audio_handler.stop_recording()
        self.audio_handler.stop_playback()

        # Stop Gemini listening
        if self._conversation_task:
            await self.gemini_client.stop_listening()
            try:
                await asyncio.wait_for(self._conversation_task, timeout=2.0)
            except asyncio.TimeoutError:
                logger.warning("Conversation task did not stop gracefully")
                self._conversation_task.cancel()

        # Disconnect from Gemini
        await self.gemini_client.disconnect()

        # Clean up audio
        self.audio_handler.cleanup()

        logger.info("Voice assistant stopped")

    def _setup_callbacks(self) -> None:
        """Set up callbacks for Gemini client."""
        self.gemini_client.set_callbacks(
            on_audio_response=self._on_audio_response,
            on_text_response=self._on_text_response,
            on_transcription=self._on_gemini_transcription,
            on_error=self._on_gemini_error,
        )

    def _on_audio_data(self, audio_data: bytes) -> None:
        """Handle incoming audio data from microphone."""
        if not self.is_running:
            return

        # Add to buffer for processing
        self.audio_buffer.extend(audio_data)
        self.last_audio_time = time.time()

        # Send to Gemini Live API
        asyncio.create_task(self._send_audio_to_gemini(audio_data))

    async def _send_audio_to_gemini(self, audio_data: bytes) -> None:
        """Send audio data to Gemini Live API."""
        try:
            await self.gemini_client.send_audio(audio_data)
        except Exception as e:
            logger.error(f"Failed to send audio to Gemini: {e}")
            if self.on_error:
                self.on_error(e)

    def _on_audio_response(self, audio_data: bytes) -> None:
        """Handle audio response from Gemini."""
        if not self.is_running:
            return

        self._set_state(ConversationState.SPEAKING)

        # Play the audio response
        self.audio_handler.play_audio(audio_data)

        # Add to conversation history
        turn = ConversationTurn(
            timestamp=datetime.now(),
            speaker="assistant",
            content="[Audio Response]",
            content_type="audio"
        )
        self.conversation_history.append(turn)

        logger.debug("Playing audio response from Gemini")

    def _on_text_response(self, text: str) -> None:
        """Handle text response from Gemini."""
        if not self.is_running:
            return

        # Add to conversation history
        turn = ConversationTurn(
            timestamp=datetime.now(),
            speaker="assistant",
            content=text,
            content_type="text"
        )
        self.conversation_history.append(turn)

        logger.info(f"Assistant: {text}")

        if self.on_transcription:
            self.on_transcription(f"Assistant: {text}")

    def _on_gemini_transcription(self, transcription: str) -> None:
        """Handle transcription from Gemini."""
        logger.info(f"Transcription: {transcription}")

        if self.on_transcription:
            self.on_transcription(transcription)

        # Update conversation history with transcription
        if self.conversation_history:
            last_turn = self.conversation_history[-1]
            if (last_turn.content_type == "audio" and
                    not last_turn.transcription):
                last_turn.transcription = transcription

    def _on_gemini_error(self, error: Exception) -> None:
        """Handle error from Gemini."""
        logger.error(f"Gemini error: {error}")
        self._set_state(ConversationState.ERROR)

        if self.on_error:
            self.on_error(error)

    def _set_state(self, new_state: ConversationState) -> None:
        """Set the conversation state."""
        if self.state != new_state:
            old_state = self.state
            self.state = new_state
            logger.debug(
                f"State changed: {old_state.value} -> {new_state.value}")

            if self.on_state_change:
                self.on_state_change(old_state, new_state)

    async def send_text_message(self, text: str) -> None:
        """Send a text message to the assistant."""
        if not self.is_running:
            raise RuntimeError("Voice assistant is not running")

        try:
            # Add to conversation history
            turn = ConversationTurn(
                timestamp=datetime.now(),
                speaker="user",
                content=text,
                content_type="text"
            )
            self.conversation_history.append(turn)

            logger.info(f"User: {text}")

            # Send to Gemini
            await self.gemini_client.send_text(text)

            self._set_state(ConversationState.PROCESSING)

        except Exception as e:
            logger.error(f"Failed to send text message: {e}")
            self._set_state(ConversationState.ERROR)
            if self.on_error:
                self.on_error(e)

    def get_conversation_history(self) -> List[ConversationTurn]:
        """Get the conversation history."""
        return self.conversation_history.copy()

    def clear_conversation_history(self) -> None:
        """Clear the conversation history."""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")

    def set_callbacks(
        self,
        on_state_change: Optional[callable] = None,
        on_transcription: Optional[callable] = None,
        on_error: Optional[callable] = None,
    ) -> None:
        """Set callback functions for UI updates."""
        if on_state_change:
            self.on_state_change = on_state_change
        if on_transcription:
            self.on_transcription = on_transcription
        if on_error:
            self.on_error = on_error

    @property
    def current_state(self) -> ConversationState:
        """Get the current conversation state."""
        return self.state

    @property
    def running(self) -> bool:
        """Check if the voice assistant is running."""
        return self.is_running

    @property
    def connected(self) -> bool:
        """Check if connected to Gemini Live API."""
        return self.gemini_client.connected
