"""
Terminal-based Voice Assistant using Vertex AI and Gemini Live API.

This package provides a complete voice assistant implementation that:
- Records audio from microphone
- Streams audio to Gemini Live API
- Receives and plays back AI responses
- Handles real-time conversation flow
"""

__version__ = "1.0.0"
__author__ = "Voice Assistant Team"

from .config import Config, AudioConfig, GeminiConfig, LoggingConfig
from .audio_handler import AudioHandler
from .gemini_client import GeminiLiveClient
from .voice_assistant import (
    VoiceAssistant,
    ConversationState,
    ConversationTurn
)

__all__ = [
    "Config",
    "AudioConfig",
    "GeminiConfig",
    "LoggingConfig",
    "AudioHandler",
    "GeminiLiveClient",
    "VoiceAssistant",
    "ConversationState",
    "ConversationTurn",
]
