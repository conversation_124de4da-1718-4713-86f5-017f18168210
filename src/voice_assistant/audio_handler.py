"""
Audio input/output handling for the Voice Assistant.

This module provides audio recording from microphone, audio playback,
and audio format conversion utilities for the Gemini Live API.
"""

import asyncio
import logging
import threading
import time
from typing import Optional, Callable, AsyncGenerator
import numpy as np
import pyaudio
import soundfile as sf
import librosa
from io import BytesIO
from queue import Queue, Empty

from .config import AudioConfig


logger = logging.getLogger(__name__)


class AudioHandler:
    """Handles audio input/output operations for the voice assistant."""

    def __init__(self, config: AudioConfig):
        """Initialize the audio handler with configuration."""
        self.config = config
        self.pyaudio_instance = None
        self.input_stream = None
        self.output_stream = None
        self.is_recording = False
        self.is_playing = False
        self.audio_queue = Queue()
        self.playback_queue = Queue()
        self._recording_thread = None
        self._playback_thread = None

        # Audio format settings for Gemini Live API
        self.input_format = pyaudio.paInt16  # 16-bit PCM
        self.output_format = pyaudio.paInt16  # 16-bit PCM

        logger.info(f"AudioHandler initialized with config: {config}")

    def __enter__(self):
        """Context manager entry."""
        self.initialize()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()

    def initialize(self) -> None:
        """Initialize PyAudio and audio streams."""
        try:
            self.pyaudio_instance = pyaudio.PyAudio()
            logger.info("PyAudio initialized successfully")

            # List available audio devices for debugging
            self._list_audio_devices()

        except Exception as e:
            logger.error(f"Failed to initialize PyAudio: {e}")
            raise RuntimeError(f"Audio initialization failed: {e}") from e

    def cleanup(self) -> None:
        """Clean up audio resources."""
        logger.info("Cleaning up audio resources")

        self.stop_recording()
        self.stop_playback()

        if self.input_stream:
            self.input_stream.close()
            self.input_stream = None

        if self.output_stream:
            self.output_stream.close()
            self.output_stream = None

        if self.pyaudio_instance:
            self.pyaudio_instance.terminate()
            self.pyaudio_instance = None

    def _list_audio_devices(self) -> None:
        """List available audio devices for debugging."""
        if not self.pyaudio_instance:
            return

        logger.info("Available audio devices:")
        for i in range(self.pyaudio_instance.get_device_count()):
            device_info = self.pyaudio_instance.get_device_info_by_index(i)
            logger.info(
                f"  Device {i}: {device_info['name']} "
                f"(inputs: {device_info['maxInputChannels']}, "
                f"outputs: {device_info['maxOutputChannels']})"
            )

    def start_recording(self, callback: Optional[Callable[[bytes], None]] = None) -> None:
        """Start recording audio from microphone."""
        if self.is_recording:
            logger.warning("Recording already in progress")
            return

        try:
            # Create input stream
            self.input_stream = self.pyaudio_instance.open(
                format=self.input_format,
                channels=self.config.channels,
                rate=self.config.sample_rate,
                input=True,
                input_device_index=self.config.input_device,
                frames_per_buffer=self.config.chunk_size,
                stream_callback=self._input_callback if not callback else None,
            )

            self.is_recording = True

            if callback:
                # Use custom callback in a separate thread
                self._recording_thread = threading.Thread(
                    target=self._recording_loop,
                    args=(callback,),
                    daemon=True
                )
                self._recording_thread.start()

            logger.info("Audio recording started")

        except Exception as e:
            logger.error(f"Failed to start recording: {e}")
            raise RuntimeError(f"Recording start failed: {e}") from e

    def stop_recording(self) -> None:
        """Stop recording audio."""
        if not self.is_recording:
            return

        self.is_recording = False

        if self.input_stream and self.input_stream.is_active():
            self.input_stream.stop_stream()

        if self._recording_thread and self._recording_thread.is_alive():
            self._recording_thread.join(timeout=1.0)

        logger.info("Audio recording stopped")

    def _input_callback(self, in_data, frame_count, time_info, status):
        """PyAudio input stream callback."""
        if status:
            logger.warning(f"Audio input status: {status}")

        # Add audio data to queue
        self.audio_queue.put(in_data)
        return (None, pyaudio.paContinue)

    def _recording_loop(self, callback: Callable[[bytes], None]) -> None:
        """Recording loop for custom callback."""
        while self.is_recording:
            try:
                if self.input_stream and self.input_stream.is_active():
                    # Read audio data
                    audio_data = self.input_stream.read(
                        self.config.chunk_size,
                        exception_on_overflow=False
                    )
                    callback(audio_data)
                else:
                    time.sleep(0.01)
            except Exception as e:
                logger.error(f"Error in recording loop: {e}")
                break

    async def get_audio_stream(self) -> AsyncGenerator[bytes, None]:
        """Get audio stream as async generator."""
        while self.is_recording:
            try:
                audio_data = self.audio_queue.get(timeout=0.1)
                yield audio_data
            except Empty:
                await asyncio.sleep(0.01)

    def start_playback(self) -> None:
        """Start audio playback."""
        if self.is_playing:
            logger.warning("Playback already in progress")
            return

        try:
            # Create output stream
            self.output_stream = self.pyaudio_instance.open(
                format=self.output_format,
                channels=self.config.channels,
                rate=24000,  # Gemini Live API outputs at 24kHz
                output=True,
                output_device_index=self.config.output_device,
                frames_per_buffer=self.config.chunk_size,
            )

            self.is_playing = True

            # Start playback thread
            self._playback_thread = threading.Thread(
                target=self._playback_loop,
                daemon=True
            )
            self._playback_thread.start()

            logger.info("Audio playback started")

        except Exception as e:
            logger.error(f"Failed to start playback: {e}")
            raise RuntimeError(f"Playback start failed: {e}") from e

    def stop_playback(self) -> None:
        """Stop audio playback."""
        if not self.is_playing:
            return

        self.is_playing = False

        if self.output_stream and self.output_stream.is_active():
            self.output_stream.stop_stream()

        if self._playback_thread and self._playback_thread.is_alive():
            self._playback_thread.join(timeout=1.0)

        logger.info("Audio playback stopped")

    def play_audio(self, audio_data: bytes) -> None:
        """Add audio data to playback queue."""
        if self.is_playing:
            self.playback_queue.put(audio_data)

    def _playback_loop(self) -> None:
        """Playback loop for audio output."""
        while self.is_playing:
            try:
                audio_data = self.playback_queue.get(timeout=0.1)
                if self.output_stream and self.output_stream.is_active():
                    self.output_stream.write(audio_data)
            except Empty:
                time.sleep(0.01)
            except Exception as e:
                logger.error(f"Error in playback loop: {e}")
                break

    @staticmethod
    def convert_audio_format(
        audio_data: bytes,
        input_rate: int = 16000,
        output_rate: int = 16000,
        input_channels: int = 1,
        output_channels: int = 1,
    ) -> bytes:
        """Convert audio format for Gemini Live API compatibility."""
        try:
            # Convert bytes to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16)

            # Convert to float for processing
            audio_float = audio_array.astype(np.float32) / 32768.0

            # Resample if needed
            if input_rate != output_rate:
                audio_float = librosa.resample(
                    audio_float,
                    orig_sr=input_rate,
                    target_sr=output_rate
                )

            # Convert channels if needed
            if input_channels != output_channels:
                if input_channels == 1 and output_channels == 2:
                    # Mono to stereo
                    audio_float = np.stack([audio_float, audio_float], axis=-1)
                elif input_channels == 2 and output_channels == 1:
                    # Stereo to mono
                    audio_float = np.mean(audio_float, axis=-1)

            # Convert back to int16
            audio_int16 = (audio_float * 32767).astype(np.int16)

            return audio_int16.tobytes()

        except Exception as e:
            logger.error(f"Audio format conversion failed: {e}")
            return audio_data  # Return original data if conversion fails

    @staticmethod
    def save_audio_to_file(
        audio_data: bytes,
        filename: str,
        sample_rate: int = 16000
    ) -> None:
        """Save audio data to a WAV file."""
        try:
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            sf.write(filename, audio_array, sample_rate)
            logger.info(f"Audio saved to {filename}")
        except Exception as e:
            logger.error(f"Failed to save audio to {filename}: {e}")

    @staticmethod
    def load_audio_from_file(filename: str, target_rate: int = 16000) -> bytes:
        """Load audio data from a file and convert to target format."""
        try:
            audio_data, sample_rate = sf.read(filename)

            # Ensure mono
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)

            # Resample if needed
            if sample_rate != target_rate:
                audio_data = librosa.resample(
                    audio_data,
                    orig_sr=sample_rate,
                    target_sr=target_rate
                )

            # Convert to int16
            audio_int16 = (audio_data * 32767).astype(np.int16)

            logger.info(f"Audio loaded from {filename}")
            return audio_int16.tobytes()

        except Exception as e:
            logger.error(f"Failed to load audio from {filename}: {e}")
            raise RuntimeError(f"Audio loading failed: {e}") from e
