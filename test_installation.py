#!/usr/bin/env python3
"""
Installation test script for Gemini Live Voice Assistant.

This script tests that all dependencies are properly installed
and the basic components can be imported and initialized.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")

    try:
        # Test standard library imports
        import asyncio
        import logging
        import json
        print("✓ Standard library imports successful")

        # Test third-party imports
        import numpy as np
        import pyaudio
        import soundfile as sf
        import librosa
        from rich.console import Console
        import click
        from dotenv import load_dotenv
        print("✓ Third-party imports successful")

        # Test Google Cloud imports
        from google import genai
        from google.auth import default
        print("✓ Google Cloud imports successful")

        # Test voice assistant imports
        from voice_assistant import Config, AudioHandler, GeminiLiveClient, VoiceAssistant
        print("✓ Voice assistant imports successful")

        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_audio_devices():
    """Test audio device detection."""
    print("\nTesting audio devices...")

    try:
        import pyaudio

        p = pyaudio.PyAudio()
        device_count = p.get_device_count()

        print(f"Found {device_count} audio devices:")

        input_devices = []
        output_devices = []

        for i in range(device_count):
            device_info = p.get_device_info_by_index(i)
            name = device_info['name']
            max_inputs = device_info['maxInputChannels']
            max_outputs = device_info['maxOutputChannels']

            print(f"  Device {i}: {name}")
            print(f"    Inputs: {max_inputs}, Outputs: {max_outputs}")

            if max_inputs > 0:
                input_devices.append(i)
            if max_outputs > 0:
                output_devices.append(i)

        p.terminate()

        if input_devices:
            print(f"✓ Found {len(input_devices)} input devices")
        else:
            print("⚠️  No input devices found")

        if output_devices:
            print(f"✓ Found {len(output_devices)} output devices")
        else:
            print("⚠️  No output devices found")

        return len(input_devices) > 0 and len(output_devices) > 0

    except Exception as e:
        print(f"❌ Audio device test failed: {e}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")

    try:
        from voice_assistant import Config

        # Test default configuration
        config = Config.from_env()
        print("✓ Default configuration loaded")

        # Test configuration validation
        try:
            config.validate()
            print("⚠️  Configuration validation passed (may need API keys)")
        except ValueError as e:
            print(f"⚠️  Configuration validation failed (expected): {e}")

        # Test logging setup
        config.setup_logging()
        print("✓ Logging setup successful")

        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_audio_handler():
    """Test audio handler initialization."""
    print("\nTesting audio handler...")

    try:
        from voice_assistant import AudioHandler, AudioConfig

        # Create audio config
        audio_config = AudioConfig()

        # Test audio handler initialization
        audio_handler = AudioHandler(audio_config)
        print("✓ Audio handler created")

        # Test context manager
        with audio_handler:
            print("✓ Audio handler context manager works")

        return True

    except Exception as e:
        print(f"❌ Audio handler test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🎤 Gemini Live Voice Assistant - Installation Test")
    print("=" * 50)

    tests = [
        ("Import Test", test_imports),
        ("Audio Devices Test", test_audio_devices),
        ("Configuration Test", test_configuration),
        ("Audio Handler Test", test_audio_handler),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))

        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✓ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\nPassed: {passed}/{total}")

    if passed == total:
        print("\n🎉 All tests passed! The installation looks good.")
        print("\nNext steps:")
        print("1. Set up your Google Cloud credentials")
        print("2. Configure your .env file")
        print("3. Run: python main.py")
    else:
        print(
            f"\n⚠️  {total - passed} test(s) failed. "
            "Please check the errors above."
        )
        print("\nTroubleshooting:")
        print("1. Make sure you activated the virtual environment")
        print("2. Install missing dependencies: "
              "uv pip install -r requirements.txt")
        print("3. Check the README for system dependencies")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
