# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# Gemini API Configuration
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-live-2.5-flash

# Audio Configuration
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_CHUNK_SIZE=1024
AUDIO_FORMAT=int16

# Voice Assistant Configuration
VOICE_NAME=Aoede
LANGUAGE_CODE=en-US
RESPONSE_MODALITY=AUDIO
ENABLE_TRANSCRIPTION=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/voice_assistant.log